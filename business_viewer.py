import os
import sys
import pickle
from googleapiclient.discovery import build
from google_auth_oauthlib.flow import InstalledAppFlow
from google.auth.transport.requests import Request
from tabulate import tabulate
import msvcrt # For Windows specific key press detection

# --- Configuration ---
SCOPES = ['https://www.googleapis.com/auth/business.manage']
ACCOUNT_API_SERVICE_NAME = 'mybusinessaccountmanagement'
INFO_API_SERVICE_NAME = 'mybusinessbusinessinformation'
API_VERSION = 'v1'
CLIENT_SECRET_FILE = 'client_secret.json'
TOKEN_PICKLE_FILE = 'token.pickle'

def get_key():
    """Gets a single key press from the user without needing to press Enter."""
    if sys.platform == "win32":
        return msvcrt.getch().decode('utf-8').lower()
    else:
        return input("Press 'r' or 'e' then Enter: ").lower()

def authenticate_gmb():
    """Handles the OAuth 2.0 authentication flow."""
    creds = None
    if os.path.exists(TOKEN_PICKLE_FILE):
        with open(TOKEN_PICKLE_FILE, 'rb') as token:
            creds = pickle.load(token)

    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            creds.refresh(Request())
        else:
            print("Authentication required. Your web browser will now open.")
            print("Please log in with your 'mpnyirongo' account and grant permission.")
            flow = InstalledAppFlow.from_client_secrets_file(
                CLIENT_SECRET_FILE, SCOPES)
            creds = flow.run_local_server(port=0)
        with open(TOKEN_PICKLE_FILE, 'wb') as token:
            pickle.dump(creds, token)
    return creds

def get_business_data(account_service, info_service):
    """
    Fetches business data by first finding all accounts and then searching
    for locations within each of them.
    """
    print("Fetching your business profiles...")
    all_locations_data = []
    try:
        # 1. Get the list of ALL accounts you have access to.
        account_list = account_service.accounts().list().execute()
        
        if not account_list.get('accounts'):
            print("No Google Business Accounts found for this user.")
            print("Please ensure your 'mpnyirongo' account is a manager or owner of a Business Profile.")
            return []
        
        print(f"Found {len(account_list['accounts'])} account(s). Checking each for locations...")

        # 2. Loop through each account to find the one with locations.
        for account in account_list['accounts']:
            account_name = account.get('accountName', 'N/A')
            account_id = account.get('name', 'N/A')
            account_type = account.get('type', 'N/A')
            
            print(f"\n---> Checking Account: '{account_name}' (Type: {account_type}, ID: {account_id})")

            # 3. Get all locations within this specific account.
            locations_list = info_service.accounts().locations().list(
                parent=account_id,
                readMask="title,name,storefrontAddress"
            ).execute()
            
            if not locations_list.get('locations'):
                print("     No locations found in this account.")
                continue

            print(f"     Success! Found {len(locations_list['locations'])} location(s) here.")
            # 4. Prepare the data for our table.
            for location in locations_list.get('locations', []):
                name = location.get('title', 'N/A')
                address_info = location.get('storefrontAddress', {})
                address_lines = address_info.get('addressLines', ['N/A'])
                full_address = ', '.join(address_lines)
                location_id = location.get('name', 'N/A')
                
                all_locations_data.append([name, full_address, location_id])
        
        return all_locations_data

    except Exception as e:
        print(f"An error occurred: {e}")
        if "is not enabled" in str(e):
             print("Please ensure both 'My Business Account Management API' and 'My Business Business Information API' are enabled in your Google Cloud project.")
        return []

def display_table(data):
    """Clears the screen and displays data in a formatted table."""
    os.system('cls' if os.name == 'nt' else 'clear')
    
    headers = ["Business Name", "Address", "Location ID"]
    if not data:
        print("--- Mwila's Google Business Profile Viewer ---")
        print("\nCould not find any locations in any of your associated accounts.")
        print("Possible Reasons:")
        print("1. API permissions haven't finished propagating (wait 5-10 minutes).")
        print("2. The account used for login does not have manager/owner access to any locations.")
        print("3. The Google Business Profile service might be disabled in your Google Workspace admin settings.")
    else:
        print("--- Mwila's Google Business Profile Viewer ---")
        print(tabulate(data, headers=headers, tablefmt="grid"))
    
    print("\nPress 'r' to refresh the data, or 'e' to exit the script.")


def main():
    """Main function to run the application loop."""
    print("Welcome, Mwila!")
    credentials = authenticate_gmb()
    
    account_service = build(ACCOUNT_API_SERVICE_NAME, API_VERSION, credentials=credentials)
    info_service = build(INFO_API_SERVICE_NAME, API_VERSION, credentials=credentials)

    while True:
        business_data = get_business_data(account_service, info_service)
        display_table(business_data)
        
        key = get_key()
        
        if key == 'e':
            print("Exiting...")
            break
        elif key == 'r':
            print("Refreshing...")
            continue

if __name__ == '__main__':
    main()
